<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Computer Use - Sandbox on AWS Demo UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            font-weight: 600;
        }
        .coming-soon-badge {
            font-size: 0.65rem;
            margin-left: 5px;
            padding: 0.25em 0.5em;
            vertical-align: middle;
            font-weight: normal;
        }

        /* Force left alignment for all log content */
        #log-container, #logs, .log-entry {
            text-align: left !important;
            justify-content: flex-start !important;
            align-items: flex-start !important;
        }

        /* Ensure badges and timestamps don't get centered */
        #logs .log-entry .badge,
        #logs .log-entry .text-muted {
            margin-left: 0 !important;
            text-align: left !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row header">
            <div class="col-12">
                <h1>Sandbox on AWS Demo UI</h1>
            </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="sandboxTabs">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sandbox-lifecycle">Sandbox Lifecycle</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/browser-use">Browser Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/code-interpreter">Code Interpreter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/computer-use">Computer Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-search">AI Search <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-ppt">AI PPT <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="row main-content">
            <!-- Left panel: Desktop stream -->
            <div class="col-md-7 stream-panel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3>Desktop Stream - Claude Computer Use on E2B</h3>
                        <div id="sandbox-controls" class="d-flex align-items-center" style="display: none;">
                            <span id="sandbox-timer" class="badge bg-secondary me-2 py-2 px-3 fs-6">00:00</span>
                            <span id="sandbox-id" class="badge bg-info me-2"></span>
                            <button id="stop-desktop" class="btn btn-danger py-1 px-2 fs-6">Stop</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="stream-container">
                            <div id="stream-placeholder">
                                <p>No active stream. Start a new desktop to begin computer use tasks.</p>
                            </div>
                            <iframe id="stream-frame" style="display: none;"></iframe>
                        </div>
                        <!-- Screenshot display area -->
                        <div id="screenshot-container" style="display: none;">
                            <img id="current-screenshot" class="img-fluid" style="cursor: crosshair;" />
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right panel: Controls and logs -->
            <div class="col-md-5 control-panel">
                <div class="card">
                    <div class="card-header">
                        <h3>Computer Use Controls</h3>
                    </div>
                    <div class="card-body">
                        <form id="computer-use-form">
                            <div class="mb-3">
                                <label for="task-input" class="form-label">Task for Claude:</label>
                                <textarea id="task-input" class="form-control" rows="4" placeholder="Describe what you want Claude to do on the computer..."></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Example Computer Use Tasks:</label>
                                <div class="d-flex flex-wrap gap-2 mb-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open Firefox and search for 'latest AI news' on Google">Web Browse</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open VS Code and create a Python script that prints 'Hello World'">Code Editor</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open the file manager and create a new folder called 'test-folder'">File Mgmt</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open LibreOffice Writer and write a short paragraph about AI">Document</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open the terminal and run 'ls -la' to list files">Terminal</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Take a screenshot and tell me what applications are visible on the desktop">Screenshot</button>
                                </div>
                            </div>
                            
                            <div class="d-flex flex-wrap gap-2 mb-4">
                                <button type="button" id="start-computer-use" class="btn btn-success">Start Desktop & Task</button>
                                <button type="button" id="run-computer-task" class="btn btn-primary" disabled>Run Task</button>
                                <button type="button" id="take-screenshot" class="btn btn-info" disabled>Take Screenshot</button>
                                <button type="button" id="stop-task" class="btn btn-warning" disabled>Stop Task</button>
                            </div>
                            
                            <!-- Action display -->
                            <div id="current-action" class="alert alert-info" style="display: none;">
                                <strong>Current Action:</strong> <span id="action-description"></span>
                            </div>
                            
                            <!-- Reasoning display -->
                            <div id="reasoning-section" style="display: none;">
                                <h5>Claude's Reasoning:</h5>
                                <div id="reasoning-content" class="border rounded p-3 bg-light mb-3" style="max-height: 200px; overflow-y: auto;"></div>
                            </div>
                            
                            <div class="logs-section mt-4" style="text-align: left !important;">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h4>Computer Use Logs</h4>
                                    <button id="clear-logs" class="btn btn-sm btn-outline-secondary">Clear</button>
                                </div>
                                <div id="log-container" style="text-align: left !important;">
                                    <div id="logs" style="text-align: left !important;"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/computer-use.js"></script>
</body>
</html>
